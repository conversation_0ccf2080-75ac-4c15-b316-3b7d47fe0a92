import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    flex: 1,
    paddingTop: 8
  },
  tabContainer: {
    flexDirection: "row",
    gap: 16,
    paddingLeft: 24,
    borderBottomWidth: 1,
    borderBottomColor: "#667085"
  },
  tab: {
    paddingHorizontal: 4,
    paddingVertical: 1,
    paddingTop: 1,
    minHeight: 32,
    justifyContent: "center",
    alignItems: "flex-start",
    borderBottomWidth: 2,
    borderBottomColor: "transparent"
  },
  activeTab: {
    borderBottomColor: "#FCFCFD",
    borderBottomWidth: 2
  },
  tabText: {
    color: "#FCFCFD",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    opacity: 0.7
  },
  activeTabText: {
    fontWeight: "700",
    opacity: 1,
    color: "#FCFCFD"
  },
  scrollView: {
    flex: 1,
    paddingTop: 16
  },
  itemCard: {
    backgroundColor: "#2A2D36", // Dark card background matching prototype
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 16,
    marginHorizontal: 24,
    marginBottom: 8,
    flexDirection: "row",
    alignItems: "center",
    gap: 12
  },
  iconContainer: {
    width: 40,
    height: 40,
    backgroundColor: "rgba(255, 255, 255, 0.1)", // Semi-transparent background for icon
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center"
  },
  itemContent: {
    flex: 1,
    justifyContent: "center"
  },
  itemTitle: {
    color: "#FFFFFF",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    marginBottom: 4
  },
  itemQuantity: {
    color: "#9CA3AF", // Gray text for quantity
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 16
  },
  itemRight: {
    alignItems: "flex-end",
    justifyContent: "center",
    minWidth: 80
  },
  itemPrice: {
    color: "#FFFFFF",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20,
    textAlign: "right"
  },
  itemStatus: {
    color: "#FF8A00", // Orange color for "Encerrado" status matching prototype
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "500",
    lineHeight: 16,
    textAlign: "right",
    marginBottom: 2
  },
  itemDate: {
    color: "#9CA3AF", // Gray text for date
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 16,
    textAlign: "right"
  }
});

export default styles;
