import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    flex: 1,
    paddingTop: 8
  },
  tabContainer: {
    flexDirection: "row",
    gap: 16,
    paddingLeft: 24,
    borderBottomWidth: 1,
    borderBottomColor: "#667085"
  },
  tab: {
    paddingHorizontal: 4,
    paddingVertical: 1,
    paddingTop: 1,
    minHeight: 32,
    justifyContent: "center",
    alignItems: "flex-start",
    borderBottomWidth: 2,
    borderBottomColor: "transparent"
  },
  activeTab: {
    borderBottomColor: "#FCFCFD",
    borderBottomWidth: 2
  },
  tabText: {
    color: "#FCFCFD",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    opacity: 0.7
  },
  activeTabText: {
    fontWeight: "700",
    opacity: 1,
    color: "#FCFCFD"
  },
  monthLabel: {
    color: "#90A0AE",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    marginTop: 12,
    marginLeft: 24,
    marginRight: 24,
    minHeight: 18
  },
  purchaseItem: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 12,
    paddingBottom: 12,
    marginBottom: 4,
    marginLeft: 24,
    marginRight: 24,
    flexDirection: "row",
    alignItems: "center",
    gap: 12
  },
  purchaseIcon: {
    width: 32,
    height: 32,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 2,
    marginBottom: 2
  },
  purchaseContent: {
    flex: 1,
    justifyContent: "center",
    minHeight: 36
  },
  purchaseTitle: {
    color: "#FFFFFF",
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    lineHeight: 18,
    flex: 1,
    marginRight: 8
  },
  purchaseDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    opacity: 0.8,
    flex: 1,
    marginRight: 8
  },
  purchaseRight: {
    gap: 2,
    flexDirection: "column",
    alignItems: "stretch",
    minWidth: 52
  },
  purchaseStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    marginBottom: 6
  },
  statusText: {
    fontFamily: stylesConstants.fonts.openSans,
    lineHeight: 18,
    minHeight: 18,
    textOverflow: "ellipsis"
  },
  purchasePrice: {
    color: "#FFFFFF",
    fontFamily: stylesConstants.fonts.openSans,
    lineHeight: 16,
    minHeight: 16
  },
  cancelledPrice: {
    textDecorationLine: "line-through"
  },
  // Status colors
  pendingStatus: {
    backgroundColor: "transparent"
  },
  pendingText: {
    color: "#FDB022"
  },
  completedStatus: {
    backgroundColor: "transparent"
  },
  completedText: {
    color: "#47CD89"
  },
  cancelledStatus: {
    backgroundColor: "transparent"
  },
  cancelledText: {
    color: "#F97066"
  },
  // Detail Modal Styles - Updated to match Motiff prototype
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(138, 138, 138, 0.6)", // #8A8A8A with opacity as shown in prototype
    justifyContent: "flex-end"
  },
  modalContent: {
    backgroundColor: stylesConstants.colors.mainBackground, // #111828
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    borderWidth: 1.5,
    borderColor: "#282A2E",
    borderBottomWidth: 0,
    paddingTop: 8,
    paddingHorizontal: 0,
    paddingBottom: 34,
    maxHeight: "98%", // Increased to match prototype - takes up almost full screen
    minHeight: "95%" // Much taller to match the prototype design exactly
  },
  dragIndicator: {
    width: 44,
    height: 4,
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 2,
    alignSelf: "center",
    marginBottom: 24
  },
  modalTitle: {
    color: "#DFE9F0", // Updated to match prototype text color
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24,
    textAlign: "center",
    marginBottom: 16, // Reduced margin to match prototype spacing
    paddingHorizontal: 24
  },
  modalScrollView: {
    flex: 1
  },
  iconContainer: {
    width: 40,
    height: 40,
    backgroundColor: "rgba(255, 255, 255, 0.20)", // Updated to match prototype - semi-transparent white
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
    marginBottom: 8, // Reduced margin to match prototype spacing
    padding: 10
  },
  iconText: {
    fontSize: 16,
    color: "#FFFFFF"
  },
  purchaseTitleModal: {
    color: "#DFE9F0", // Updated to match prototype text color
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24,
    textAlign: "center",
    paddingHorizontal: 24,
    marginBottom: 16, // Reduced margin to match prototype spacing
    minHeight: 48 // Added to match prototype layout
  },
  infoSection: {
    backgroundColor: "#202938", // Updated to match prototype background color exactly
    borderRadius: 8,
    marginHorizontal: 22, // Adjusted to match prototype margins
    paddingVertical: 12, // Reduced from 16 to match prototype
    paddingHorizontal: 30, // Reduced from 16 to match prototype
    marginBottom: 24,
    marginTop: 16 // Added top margin to match prototype spacing
  },
  infoSectionTitle: {
    color: "#DFE9F0", // Updated to match prototype text color
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24,
    textAlign: "center", // Changed to center to match prototype
    marginBottom: 12, // Reduced margin to match prototype spacing
    width: "100%" // Added to ensure proper centering
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 6, // Further reduced to match prototype
    paddingVertical: 6, // Further reduced to match prototype
    minHeight: 30, // Further reduced to match prototype
    gap: 12 // Reduced gap to match prototype spacing
  },
  infoRowHighlighted: {
    backgroundColor: "#344054", // Updated to match prototype's highlighted row color
    marginHorizontal: -12, // Adjusted to match new section padding
    borderRadius: 4, // Added border radius to match prototype
    paddingHorizontal: 6 // Consistent with infoRow padding
  },
  infoRowMultiline: {
    alignItems: "flex-start"
  },
  infoLabel: {
    color: "#DFE9F0", // Updated to match prototype text color exactly
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12, // Updated to match prototype font size
    fontWeight: "400",
    lineHeight: 18, // Updated to match prototype line height
    flex: 1
  },
  infoValue: {
    color: "#DFE9F0", // Updated to match prototype text color exactly
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12, // Updated to match prototype font size
    fontWeight: "600",
    lineHeight: 18, // Updated to match prototype line height
    textAlign: "right",
    flex: 1
  },
  infoValueMultiline: {
    minHeight: 36,
    textAlign: "right"
  },
  closeButtonModal: {
    borderRadius: 8,
    paddingVertical: 10, // Adjusted to match prototype
    paddingHorizontal: 18, // Adjusted to match prototype
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
    marginTop: 24, // Reduced margin to match prototype
    marginBottom: 34, // Increased bottom margin since home indicator is removed
    backgroundColor: "transparent",
    width: 89, // Added fixed width to match prototype
    height: 44 // Added fixed height to match prototype
  },
  closeButtonTextModal: {
    color: "#DFE9F0", // Updated to match prototype text color
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24,
    width: 53, // Added to match prototype
    minHeight: 24 // Added to match prototype
  },
  // Legacy styles - keeping for backward compatibility
  detailSection: {
    marginBottom: 24
  },
  detailSectionTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24,
    marginBottom: 16
  },
  detailItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(255, 255, 255, 0.08)"
  },
  detailItemLast: {
    borderBottomWidth: 0
  },
  detailLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    opacity: 0.8
  },
  detailValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "500",
    lineHeight: 20,
    textAlign: "right",
    flex: 1,
    marginLeft: 16
  },
  closeButton: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 24
  },
  closeButtonText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "600",
    lineHeight: 24
  }
});

export default styles;
