import React, {useState} from "react";
import {Text, View, ScrollView, TouchableOpacity} from "react-native";
import <PERSON><PERSON>ithHeader from "../../components/screen-with-header";
import BookIcon from "../../components/icons/book-icon";
import CalendarIcon from "../../components/icons/calendar-icon";
import styles from "@/styles/settings/my-products.style";
import {useTranslation} from "react-i18next";

interface MyProduct {
  id: string;
  type: "product" | "ticket";
  title: string;
  quantity?: string;
  price?: number;
  status?: "active" | "expired" | "ended";
  date?: string;
}

interface MyTicket {
  id: string;
  type: "product" | "ticket";
  title: string;
  status: "active" | "expired" | "ended";
  date: string;
}

const MyProducts: React.FC = () => {
  const {t} = useTranslation();
  const [selectedTab, setSelectedTab] = useState<string>("products");

  // Sample data matching the prototype
  const [products] = useState<MyProduct[]>([
    {
      id: "1",
      type: "product",
      title: "E-book - Tendências de arquitetura em 2025",
      quantity: "1x",
      price: 1580.0
    }
  ]);

  const [tickets] = useState<MyTicket[]>([
    {
      id: "1",
      type: "ticket",
      title: "Comemoração de 10 anos da Cafeteira Garibaldi",
      status: "ended",
      date: "15/04/025"
    },
    {
      id: "2",
      type: "ticket",
      title: "Comemoração de 10 anos da Cafeteira Garibaldi",
      status: "ended",
      date: "15/04/025"
    },
    {
      id: "3",
      type: "ticket",
      title: "Comemoração de 10 anos da Cafeteira Garibaldi",
      status: "ended",
      date: "15/04/025"
    },
    {
      id: "4",
      type: "ticket",
      title: "Comemoração de 10 anos da Cafeteira Garibaldi",
      status: "ended",
      date: "15/04/025"
    }
  ]);

  const allTabs = [
    {id: "products", name: "Produtos adquiridos"},
    {id: "tickets", name: "Ingressos adquiridos"}
  ];

  // Reorder tabs to put the selected tab first
  const tabs = [
    allTabs.find((tab) => tab.id === selectedTab)!,
    ...allTabs.filter((tab) => tab.id !== selectedTab)
  ];

  const currentItems = selectedTab === "products" ? products : tickets;

  const getStatusText = (status: string) => {
    switch (status) {
      case "ended":
        return "Encerrado";
      case "active":
        return "Ativo";
      case "expired":
        return "Expirado";
      default:
        return status;
    }
  };

  const formatPrice = (price: number) => {
    return `R$ ${price.toFixed(2).replace(".", ",")}`;
  };

  return (
    <ScreenWithHeader screenTitle="Meus produtos" backButton>
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          {/* Tab Navigation */}
          <View style={styles.tabContainer}>
            {tabs.map((tab) => (
              <TouchableOpacity
                key={tab.id}
                style={[styles.tab, selectedTab === tab.id && styles.activeTab]}
                onPress={() => setSelectedTab(tab.id)}
              >
                <Text
                  style={[
                    styles.tabText,
                    selectedTab === tab.id && styles.activeTabText
                  ]}
                >
                  {tab.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Items List */}
          <ScrollView
            style={styles.scrollView}
            showsVerticalScrollIndicator={false}
          >
            {currentItems.map((item) => (
              <TouchableOpacity key={item.id} style={styles.itemCard}>
                <View style={styles.iconContainer}>
                  {item.type === "product" ? (
                    <BookIcon width={24} height={24} replaceColor="#FFFFFF" />
                  ) : (
                    <CalendarIcon
                      width={24}
                      height={24}
                      replaceColor="#FFFFFF"
                    />
                  )}
                </View>

                <View style={styles.itemContent}>
                  <Text style={styles.itemTitle} numberOfLines={2}>
                    {item.title}
                  </Text>
                  {item.type === "product" && (item as MyProduct).quantity && (
                    <Text style={styles.itemQuantity}>
                      Qtde.: {(item as MyProduct).quantity}
                    </Text>
                  )}
                </View>

                <View style={styles.itemRight}>
                  {item.type === "product" && (item as MyProduct).price ? (
                    <Text style={styles.itemPrice}>
                      {formatPrice((item as MyProduct).price!)}
                    </Text>
                  ) : (
                    <>
                      <Text style={styles.itemStatus}>
                        {getStatusText((item as MyTicket).status)}
                      </Text>
                      <Text style={styles.itemDate}>
                        {(item as MyTicket).date}
                      </Text>
                    </>
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </View>
    </ScreenWithHeader>
  );
};

export default MyProducts;
